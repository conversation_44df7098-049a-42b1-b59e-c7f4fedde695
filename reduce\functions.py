# -*- coding: utf-8 -*-
"""
整合lunar_python文件夹下所有Python文件中的函数定义
包括类方法、静态方法和普通函数
"""

# ==================== EightChar类的方法 ====================
class EightChar:
    """八字"""
    
    def __init__(self, lunar):
        self.__sect = 2
        self.__lunar = lunar

    @staticmethod
    def fromLunar(lunar):
        return EightChar(lunar)

    def toString(self):
        return self.getYear() + " " + self.getMonth() + " " + self.getDay() + " " + self.getTime()

    def __str__(self):
        return self.toString()

    def getSect(self):
        return self.__sect

    def setSect(self, sect):
        self.__sect = sect

    def getYear(self):
        """获取年柱"""
        return self.__lunar.getYearInGanZhiExact()

    def getYearGan(self):
        """获取年干"""
        return self.__lunar.getYearGanExact()

    def getYearZhi(self):
        """获取年支"""
        return self.__lunar.getYearZhiExact()

    def getYearHideGan(self):
        """获取年柱地支藏干，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        from .variables import LunarUtil_ZHI_HIDE_GAN
        return LunarUtil_ZHI_HIDE_GAN.get(self.getYearZhi())

    def getYearWuXing(self):
        """获取年柱五行"""
        from .variables import LunarUtil_WU_XING_GAN, LunarUtil_WU_XING_ZHI
        return LunarUtil_WU_XING_GAN.get(self.getYearGan()) + LunarUtil_WU_XING_ZHI.get(self.getYearZhi())

    def getYearNaYin(self):
        """获取年柱纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getYear())

    def getYearShiShenGan(self):
        """获取年柱天干十神"""
        from .variables import LunarUtil_SHI_SHEN
        return LunarUtil_SHI_SHEN.get(self.getDayGan() + self.getYearGan())

    def __getShiShenZhi(self, zhi):
        from .variables import LunarUtil_ZHI_HIDE_GAN, LunarUtil_SHI_SHEN
        hide_gan = LunarUtil_ZHI_HIDE_GAN.get(zhi)
        arr = []
        for gan in hide_gan:
            arr.append(LunarUtil_SHI_SHEN.get(self.getDayGan() + gan))
        return arr

    def getYearShiShenZhi(self):
        """获取年柱地支十神，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        return self.__getShiShenZhi(self.getYearZhi())

    def getDayGanIndex(self):
        return self.__lunar.getDayGanIndexExact2() if 2 == self.__sect else self.__lunar.getDayGanIndexExact()

    def getDayZhiIndex(self):
        return self.__lunar.getDayZhiIndexExact2() if 2 == self.__sect else self.__lunar.getDayZhiIndexExact()

    def __getDiShi(self, zhi_index):
        from .variables import EightChar_CHANG_SHENG_OFFSET, EightChar_CHANG_SHENG
        index = EightChar_CHANG_SHENG_OFFSET.get(self.getDayGan()) + (zhi_index if self.getDayGanIndex() % 2 == 0 else -zhi_index)
        if index >= 12:
            index -= 12
        if index < 0:
            index += 12
        return EightChar_CHANG_SHENG[index]

    def getYearDiShi(self):
        """获取年柱地势（长生十二神）"""
        return self.__getDiShi(self.__lunar.getYearZhiIndexExact())

    def getMonth(self):
        """获取月柱"""
        return self.__lunar.getMonthInGanZhiExact()

    def getMonthGan(self):
        """获取月干"""
        return self.__lunar.getMonthGanExact()

    def getMonthZhi(self):
        """获取月支"""
        return self.__lunar.getMonthZhiExact()

    def getMonthHideGan(self):
        """获取月柱地支藏干，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        from .variables import LunarUtil_ZHI_HIDE_GAN
        return LunarUtil_ZHI_HIDE_GAN.get(self.getMonthZhi())

    def getMonthWuXing(self):
        """获取月柱五行"""
        from .variables import LunarUtil_WU_XING_GAN, LunarUtil_WU_XING_ZHI
        return LunarUtil_WU_XING_GAN.get(self.getMonthGan()) + LunarUtil_WU_XING_ZHI.get(self.getMonthZhi())

    def getMonthNaYin(self):
        """获取月柱纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getMonth())

    def getMonthShiShenGan(self):
        """获取月柱天干十神"""
        from .variables import LunarUtil_SHI_SHEN
        return LunarUtil_SHI_SHEN.get(self.getDayGan() + self.getMonthGan())

    def getMonthShiShenZhi(self):
        """获取月柱地支十神，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        return self.__getShiShenZhi(self.getMonthZhi())

    def getMonthDiShi(self):
        """获取月柱地势（长生十二神）"""
        return self.__getDiShi(self.__lunar.getMonthZhiIndexExact())

    def getDay(self):
        """获取日柱"""
        return self.__lunar.getDayInGanZhiExact2() if 2 == self.__sect else self.__lunar.getDayInGanZhiExact()

    def getDayGan(self):
        """获取日干"""
        return self.__lunar.getDayGanExact2() if 2 == self.__sect else self.__lunar.getDayGanExact()

    def getDayZhi(self):
        """获取日支"""
        return self.__lunar.getDayZhiExact2() if 2 == self.__sect else self.__lunar.getDayZhiExact()

    def getDayHideGan(self):
        """获取日柱地支藏干，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        from .variables import LunarUtil_ZHI_HIDE_GAN
        return LunarUtil_ZHI_HIDE_GAN.get(self.getDayZhi())

    def getDayWuXing(self):
        """获取日柱五行"""
        from .variables import LunarUtil_WU_XING_GAN, LunarUtil_WU_XING_ZHI
        return LunarUtil_WU_XING_GAN.get(self.getDayGan()) + LunarUtil_WU_XING_ZHI.get(self.getDayZhi())

    def getDayNaYin(self):
        """获取日柱纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getDay())

    def getDayShiShenGan(self):
        """获取日柱天干十神，也称日元、日干"""
        return "日主"

    def getDayShiShenZhi(self):
        """获取日柱地支十神，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        return self.__getShiShenZhi(self.getDayZhi())

    def getDayDiShi(self):
        """获取日柱地势（长生十二神）"""
        return self.__getDiShi(self.getDayZhiIndex())

    def getTime(self):
        """获取时柱"""
        return self.__lunar.getTimeInGanZhi()

    def getTimeGan(self):
        """获取时干"""
        return self.__lunar.getTimeGan()

    def getTimeZhi(self):
        """获取时支"""
        return self.__lunar.getTimeZhi()

    def getTimeHideGan(self):
        """获取时柱地支藏干，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        from .variables import LunarUtil_ZHI_HIDE_GAN
        return LunarUtil_ZHI_HIDE_GAN.get(self.getTimeZhi())

    def getTimeWuXing(self):
        """获取时柱五行"""
        from .variables import LunarUtil_WU_XING_GAN, LunarUtil_WU_XING_ZHI
        return LunarUtil_WU_XING_GAN.get(self.getTimeGan()) + LunarUtil_WU_XING_ZHI.get(self.getTimeZhi())

    def getTimeNaYin(self):
        """获取时柱纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getTime())

    def getTimeShiShenGan(self):
        """获取时柱天干十神"""
        from .variables import LunarUtil_SHI_SHEN
        return LunarUtil_SHI_SHEN.get(self.getDayGan() + self.getTimeGan())

    def getTimeShiShenZhi(self):
        """获取时柱地支十神，由于藏干分主气、余气、杂气，所以返回结果可能为1到3个元素"""
        return self.__getShiShenZhi(self.getTimeZhi())

    def getTimeDiShi(self):
        """获取时柱地势（长生十二神）"""
        return self.__getDiShi(self.__lunar.getTimeZhiIndex())

    def getTaiYuan(self):
        """获取胎元"""
        from .variables import LunarUtil_GAN, LunarUtil_ZHI
        gan_index = self.__lunar.getMonthGanIndexExact() + 1
        if gan_index >= 10:
            gan_index -= 10
        zhi_index = self.__lunar.getMonthZhiIndexExact() + 3
        if zhi_index >= 12:
            zhi_index -= 12
        return LunarUtil_GAN[gan_index + 1] + LunarUtil_ZHI[zhi_index + 1]

    def getTaiYuanNaYin(self):
        """获取胎元纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getTaiYuan())

    def getTaiXi(self):
        """获取胎息"""
        from .variables import LunarUtil_HE_GAN_5, LunarUtil_HE_ZHI_6
        gan_index = self.__lunar.getDayGanIndexExact2() if 2 == self.__sect else self.__lunar.getDayGanIndexExact()
        zhi_index = self.__lunar.getDayZhiIndexExact2() if 2 == self.__sect else self.__lunar.getDayZhiIndexExact()
        return LunarUtil_HE_GAN_5[gan_index] + LunarUtil_HE_ZHI_6[zhi_index]

    def getTaiXiNaYin(self):
        """获取胎息纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getTaiXi())

    def getMingGong(self):
        """获取命宫"""
        from .variables import EightChar_MONTH_ZHI, LunarUtil_GAN
        month_zhi_index = 0
        time_zhi_index = 0
        month_zhi = self.getMonthZhi()
        time_zhi = self.getTimeZhi()
        for i in range(0, len(EightChar_MONTH_ZHI)):
            zhi = EightChar_MONTH_ZHI[i]
            if month_zhi == zhi:
                month_zhi_index = i
                break
        for i in range(0, len(EightChar_MONTH_ZHI)):
            zhi = EightChar_MONTH_ZHI[i]
            if time_zhi == zhi:
                time_zhi_index = i
                break
        offset = month_zhi_index + time_zhi_index
        if offset >= 14:
            offset = 26 - offset
        else:
            offset = 14 - offset
        gan_index = (self.__lunar.getYearGanIndexExact() + 1) * 2 + offset
        while gan_index > 10:
            gan_index -= 10
        return LunarUtil_GAN[gan_index] + EightChar_MONTH_ZHI[offset]

    def getMingGongNaYin(self):
        """获取命宫纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getMingGong())

    def getShenGong(self):
        """获取身宫"""
        from .variables import EightChar_MONTH_ZHI, LunarUtil_ZHI, LunarUtil_GAN
        month_zhi_index = 0
        time_zhi_index = 0
        month_zhi = self.getMonthZhi()
        time_zhi = self.getTimeZhi()
        for i in range(0, len(EightChar_MONTH_ZHI)):
            zhi = EightChar_MONTH_ZHI[i]
            if month_zhi == zhi:
                month_zhi_index = i
                break
        for i in range(0, len(LunarUtil_ZHI)):
            zhi = LunarUtil_ZHI[i]
            if time_zhi == zhi:
                time_zhi_index = i
                break
        offset = month_zhi_index + time_zhi_index
        if offset > 12:
            offset -= 12
        gan_index = (self.__lunar.getYearGanIndexExact() + 1) * 2 + offset
        while gan_index > 10:
            gan_index -= 10
        return LunarUtil_GAN[gan_index] + EightChar_MONTH_ZHI[offset]

    def getShenGongNaYin(self):
        """获取身宫纳音"""
        from .variables import LunarUtil_NAYIN
        return LunarUtil_NAYIN.get(self.getShenGong())

    def getLunar(self):
        return self.__lunar

    def getYun(self, gender, sect=1):
        """获取运"""
        from .eightchar import Yun
        return Yun(self, gender, sect)

    def getYearXun(self):
        """获取年柱所在旬"""
        return self.__lunar.getYearXunExact()

    def getYearXunKong(self):
        """获取年柱旬空(空亡)"""
        return self.__lunar.getYearXunKongExact()

    def getMonthXun(self):
        """获取月柱所在旬"""
        return self.__lunar.getMonthXunExact()

    def getMonthXunKong(self):
        """获取月柱旬空(空亡)"""
        return self.__lunar.getMonthXunKongExact()

    def getDayXun(self):
        """获取日柱所在旬"""
        return self.__lunar.getDayXunExact2() if 2 == self.__sect else self.__lunar.getDayXunExact()

    def getDayXunKong(self):
        """获取日柱旬空(空亡)"""
        return self.__lunar.getDayXunKongExact2() if 2 == self.__sect else self.__lunar.getDayXunKongExact()

    def getTimeXun(self):
        """获取时柱所在旬"""
        return self.__lunar.getTimeXun()

    def getTimeXunKong(self):
        """获取时柱旬空(空亡)"""
        return self.__lunar.getTimeXunKong()


# ==================== Foto类的方法 ====================
class Foto:
    """佛历"""
    
    def __init__(self, lunar):
        self.__lunar = lunar

    @staticmethod
    def fromLunar(lunar):
        return Foto(lunar)

    def getLunar(self):
        return self.__lunar

    def getYear(self):
        sy = self.__lunar.getSolar().getYear()
        y = sy - 543
        if sy == self.__lunar.getYear():
            return y
        return y - 1

    def getMonth(self):
        return self.__lunar.getMonth()

    def getDay(self):
        return self.__lunar.getDay()

    def getYearInChinese(self):
        from .variables import LunarUtil_NUMBER
        y = str(self.getYear())
        s = ""
        for i in range(0, len(y)):
            s += LunarUtil_NUMBER[ord(y[i]) - 48]
        return s

    def getMonthInChinese(self):
        return self.__lunar.getMonthInChinese()

    def getDayInChinese(self):
        return self.__lunar.getDayInChinese()

    def getFestivals(self):
        from .variables import FotoUtil_FESTIVAL
        festivals = []
        md = "%d-%d" % (abs(self.getMonth()), self.getDay())
        if md in FotoUtil_FESTIVAL:
            fs = FotoUtil_FESTIVAL[md]
            for f in fs:
                festivals.append(f)
        return festivals

    def getOtherFestivals(self):
        """获取纪念日"""
        from .variables import FotoUtil_OTHER_FESTIVAL
        festivals = []
        key = "%d-%d" % (self.getMonth(), self.getDay())
        if key in FotoUtil_OTHER_FESTIVAL:
            for f in FotoUtil_OTHER_FESTIVAL[key]:
                festivals.append(f)
        return festivals

    def isMonthZhai(self):
        m = abs(self.getMonth())
        return 1 == m or 5 == m or 9 == m

    def isDayZhai(self):
        d = self.getDay()
        if d in (1, 8, 14, 15, 18, 23, 24, 28, 29, 30):
            return True
        if abs(self.getMonth()) == 2 and d == 29:
            return True
        if abs(self.getMonth()) == 12:
            if d == 25:
                return True
            if d == 29 and self.__lunar.getSolar().getYear() % 4 != 0:
                return True
        return False

    def isDayZhaiGuanYin(self):
        from .variables import FotoUtil_DAY_ZHAI_GUAN_YIN
        k = "%d-%d" % (self.getMonth(), self.getDay())
        for d in FotoUtil_DAY_ZHAI_GUAN_YIN:
            if k == d:
                return True
        return False

    def getXiu(self):
        from .variables import FotoUtil_getXiu
        return FotoUtil_getXiu(self.getMonth(), self.getDay())

    def getXiuLuck(self):
        from .variables import LunarUtil_XIU_LUCK
        return LunarUtil_XIU_LUCK[self.getXiu()]

    def getXiuSong(self):
        from .variables import LunarUtil_XIU_SONG
        return LunarUtil_XIU_SONG[self.getXiu()]

    def getZheng(self):
        from .variables import LunarUtil_ZHENG
        return LunarUtil_ZHENG[self.getXiu()]

    def getAnimal(self):
        from .variables import LunarUtil_ANIMAL
        return LunarUtil_ANIMAL[self.getXiu()]

    def getGong(self):
        from .variables import LunarUtil_GONG
        return LunarUtil_GONG[self.getXiu()]

    def getShou(self):
        from .variables import LunarUtil_SHOU
        return LunarUtil_SHOU[self.getGong()]

    def __str__(self):
        return self.toString()

    def toString(self):
        return "%s年%s月%s" % (self.getYearInChinese(), self.getMonthInChinese(), self.getDayInChinese())


# ==================== Solar类的方法 ====================
class Solar:
    """阳历日期"""

    # 2000年儒略日数(2000-1-1 12:00:00 UTC)
    J2000 = 2451545

    def __init__(self, year, month, day, hour, minute, second):
        if year == 1582 and month == 10:
            if 4 < day < 15:
                raise Exception("wrong solar year %d month %d day %d" % (year, month, day))
        if month < 1 or month > 12:
            raise Exception("wrong month %d" % month)
        if day < 1 or month > 31:
            raise Exception("wrong day %d" % day)
        if hour < 0 or hour > 23:
            raise Exception("wrong hour %d" % hour)
        if minute < 0 or minute > 59:
            raise Exception("wrong minute %d" % minute)
        if second < 0 or second > 59:
            raise Exception("wrong second %d" % second)
        self.__year = year
        self.__month = month
        self.__day = day
        self.__hour = hour
        self.__minute = minute
        self.__second = second

    @staticmethod
    def fromYmd(year, month, day):
        return Solar(year, month, day, 0, 0, 0)

    @staticmethod
    def fromYmdHms(year, month, day, hour, minute, second):
        return Solar(year, month, day, hour, minute, second)

    @staticmethod
    def fromJulianDay(julian_day):
        d = int(julian_day + 0.5)
        f = julian_day + 0.5 - d
        if d >= 2299161:
            c = int((d - 1867216.25) / 36524.25)
            d += 1 + c - int(c / 4)
        d += 1524
        year = int((d - 122.1) / 365.25)
        d -= int(365.25 * year)
        month = int(d / 30.601)
        d -= int(30.601 * month)
        day = d
        if month > 13:
            month -= 13
            year -= 4715
        else:
            month -= 1
            year -= 4716
        f *= 24
        hour = int(f)
        f -= hour
        f *= 60
        minute = int(f)
        f -= minute
        f *= 60
        second = int(f)
        return Solar.fromYmdHms(year, month, day, hour, minute, second)

    @staticmethod
    def fromBaZi(year_gan_zhi, month_gan_zhi, day_gan_zhi, time_gan_zhi, sect=2, base_year=1900):
        from .variables import LunarUtil_find_gan_zhi
        return LunarUtil_find_gan_zhi(year_gan_zhi, month_gan_zhi, day_gan_zhi, time_gan_zhi, sect, base_year)

    def isLeapYear(self):
        from .variables import SolarUtil_isLeapYear
        return SolarUtil_isLeapYear(self.__year)

    def getWeek(self):
        from .variables import SolarUtil_getWeek
        return SolarUtil_getWeek(self.__year, self.__month, self.__day)

    def getWeekInChinese(self):
        from .variables import SolarUtil_WEEK
        return SolarUtil_WEEK[self.getWeek()]

    def getXingZuo(self):
        index = 11
        from .variables import SolarUtil_XING_ZUO_DAYS
        y = self.__month * 100 + self.__day
        if y >= 321 and y <= 419:
            index = 0
        elif y >= 420 and y <= 520:
            index = 1
        elif y >= 521 and y <= 620:
            index = 2
        elif y >= 621 and y <= 722:
            index = 3
        elif y >= 723 and y <= 822:
            index = 4
        elif y >= 823 and y <= 922:
            index = 5
        elif y >= 923 and y <= 1022:
            index = 6
        elif y >= 1023 and y <= 1121:
            index = 7
        elif y >= 1122 and y <= 1221:
            index = 8
        elif y >= 1222 or y <= 119:
            index = 9
        elif y >= 120 and y <= 218:
            index = 10
        from .variables import SolarUtil_XING_ZUO
        return SolarUtil_XING_ZUO[index]

    def getFestivals(self):
        from .variables import SolarUtil_FESTIVAL
        festivals = []
        md = "%d-%d" % (self.__month, self.__day)
        if md in SolarUtil_FESTIVAL:
            festivals.append(SolarUtil_FESTIVAL[md])
        return festivals

    def getOtherFestivals(self):
        from .variables import SolarUtil_getOtherFestivals
        return SolarUtil_getOtherFestivals(self.__year, self.__month, self.__day)

    def getJulianDay(self):
        y = self.__year
        m = self.__month
        d = self.__day + ((self.__second / 60.0 + self.__minute) / 60.0 + self.__hour) / 24.0
        n = 0
        g = False
        if y * 372 + m * 31 + int(d) >= 588829:
            g = True
        if m <= 2:
            m += 12
            y -= 1
        if g:
            n = int(y / 100)
            n = 2 - n + int(n / 4)
        return int(365.25 * (y + 4716)) + int(30.6001 * (m + 1)) + d + n - 1524.5

    def getLunar(self):
        from . import Lunar
        return Lunar.fromSolar(self)

    def nextDay(self, days):
        from .variables import SolarUtil_getDaysOfMonth
        y = self.__year
        m = self.__month
        d = self.__day
        if 1582 == y and 10 == m:
            if d > 4:
                d -= 10
        if days > 0:
            d += days
            days_in_month = SolarUtil_getDaysOfMonth(y, m)
            while d > days_in_month:
                d -= days_in_month
                m += 1
                if m > 12:
                    m = 1
                    y += 1
                days_in_month = SolarUtil_getDaysOfMonth(y, m)
        elif days < 0:
            while d + days <= 0:
                m -= 1
                if m < 1:
                    m = 12
                    y -= 1
                d += SolarUtil_getDaysOfMonth(y, m)
            d += days
        if 1582 == y and 10 == m:
            if d > 4:
                d += 10
        return Solar.fromYmdHms(y, m, d, self.__hour, self.__minute, self.__second)

    def next(self, days, only_work_day=False):
        """获取往后推几天的阳历日期，如果要往前推，则天数用负数"""
        if not only_work_day:
            return self.nextDay(days)
        from .variables import HolidayUtil_getHoliday
        solar = Solar.fromYmdHms(self.__year, self.__month, self.__day, self.__hour, self.__minute, self.__second)
        if days != 0:
            rest = abs(days)
            add = 1
            if days < 0:
                add = -1
            while rest > 0:
                solar = solar.next(add)
                work = True
                holiday = HolidayUtil_getHoliday(solar.getYear(), solar.getMonth(), solar.getDay())
                if holiday is None:
                    week = solar.getWeek()
                    if 0 == week or 6 == week:
                        work = False
                else:
                    work = holiday.isWork()
                if work:
                    rest -= 1
        return solar

    def getYear(self):
        return self.__year

    def getMonth(self):
        return self.__month

    def getDay(self):
        return self.__day

    def getHour(self):
        return self.__hour

    def getMinute(self):
        return self.__minute

    def getSecond(self):
        return self.__second

    def toYmd(self):
        return "%04d-%02d-%02d" % (self.__year, self.__month, self.__day)

    def toYmdHms(self):
        return "%s %02d:%02d:%02d" % (self.toYmd(), self.__hour, self.__minute, self.__second)

    def toFullString(self):
        s = self.toYmdHms()
        if self.isLeapYear():
            s += " 闰年"
        s += " 星期"
        s += self.getWeekInChinese()
        for f in self.getFestivals():
            s += " (" + f + ")"
        for f in self.getOtherFestivals():
            s += " (" + f + ")"
        s += " "
        s += self.getXingZuo()
        s += "座"
        return s

    def toString(self):
        return self.toYmd()

    def __str__(self):
        return self.toString()

    def subtract(self, solar):
        return int(self.getJulianDay() - solar.getJulianDay())

    def subtractMinute(self, solar):
        days = self.subtract(solar)
        cm = self.__hour * 60 + self.__minute
        sm = solar.getHour() * 60 + solar.getMinute()
        m = days * 1440 + cm - sm
        return m

    def isAfter(self, solar):
        if self.__year > solar.getYear():
            return True
        if self.__year < solar.getYear():
            return False
        if self.__month > solar.getMonth():
            return True
        if self.__month < solar.getMonth():
            return False
        if self.__day > solar.getDay():
            return True
        if self.__day < solar.getDay():
            return False
        if self.__hour > solar.getHour():
            return True
        if self.__hour < solar.getHour():
            return False
        if self.__minute > solar.getMinute():
            return True
        if self.__minute < solar.getMinute():
            return False
        return self.__second > solar.getSecond()

    def isBefore(self, solar):
        if self.__year > solar.getYear():
            return False
        if self.__year < solar.getYear():
            return True
        if self.__month > solar.getMonth():
            return False
        if self.__month < solar.getMonth():
            return True
        if self.__day > solar.getDay():
            return False
        if self.__day < solar.getDay():
            return True
        if self.__hour > solar.getHour():
            return False
        if self.__hour < solar.getHour():
            return True
        if self.__minute > solar.getMinute():
            return False
        if self.__minute < solar.getMinute():
            return True
        return self.__second < solar.getSecond()

    def nextYear(self, years):
        from .variables import SolarUtil_isLeapYear, SolarUtil_getDaysOfMonth
        y = self.__year + years
        m = self.__month
        d = self.__day
        if 1582 == y and 10 == m:
            if 4 < d < 15:
                d += 10
        elif 2 == m:
            if d > 28:
                if not SolarUtil_isLeapYear(y):
                    d = 28
        return Solar.fromYmdHms(y, m, d, self.__hour, self.__minute, self.__second)

    def nextMonth(self, months):
        from . import SolarMonth
        month = SolarMonth.fromYm(self.__year, self.__month).next(months)
        y = month.getYear()
        m = month.getMonth()
        d = self.__day
        if 1582 == y and 10 == m:
            if 4 < d < 15:
                d += 10
        else:
            from .variables import SolarUtil_getDaysOfMonth
            days = SolarUtil_getDaysOfMonth(y, m)
            if d > days:
                d = days
        return Solar.fromYmdHms(y, m, d, self.__hour, self.__minute, self.__second)
